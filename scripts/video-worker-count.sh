#!/bin/bash

set -euo pipefail

_autoscaling_group='thankview-video-workers'
_base_max_capacity=3

help() {
    echo "Usage: video-worker-count.sh [COMMAND] ..."
    echo "Display and modify the number of thankview video workers"
    echo
    echo "COMMANDs are \"check\" and \"set VALUE\""
}

check() {
    desired_capacity=`aws autoscaling describe-auto-scaling-groups \
        --auto-scaling-group-names $_autoscaling_group \
        --output text \
        --query 'AutoScalingGroups[*].DesiredCapacity'`
    echo "Desired Capacity: ${desired_capacity} instances"
}

set() {
    max_capacity=`aws autoscaling describe-auto-scaling-groups \
        --auto-scaling-group-names $_autoscaling_group \
        --output text \
        --query 'AutoScalingGroups[*].MaxSize'`
    if [ ${1} -ge ${max_capacity} ]; then
        aws autoscaling update-auto-scaling-group \
            --auto-scaling-group-name $_autoscaling_group \
            --max-size $(( $1 + 1 ))
    fi
    aws autoscaling set-desired-capacity \
        --auto-scaling-group-name $_autoscaling_group \
        --desired-capacity $1
    if [ ${1} -le ${_base_max_capacity} ]; then 
        aws autoscaling update-auto-scaling-group \
            --auto-scaling-group-name $_autoscaling_group \
            --max-size $(( $_base_max_capacity + 1 ))
    fi
    echo "Successfully set desired capacity to ${1} instances"
}

if [[ -z ${1+x} ]]; then
    help
elif [[ $1 == "check" ]]; then
    check
elif [[ $1 == "set" ]]; then
    if [[ -z ${2+x} ]]; then
        echo "Error: Must set a value for desired capacity"
        exit 1
    fi
    set $2
else 
    help
    echo "Bad command"
fi
