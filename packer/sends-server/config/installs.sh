#!/bin/bash
set -euo pipefail
sudo add-apt-repository ppa:ondrej/php
sudo add-apt-repository ppa:savoury1/ffmpeg4
sudo add-apt-repository ppa:savoury1/graphics
sudo add-apt-repository ppa:savoury1/multimedia
sudo add-apt-repository ppa:savoury1/build-tools

sudo apt-key add /tmp/nodesource.gpg.key
DISTRO="$(lsb_release -s -c)"
echo "deb https://deb.nodesource.com/node_10.x $DISTRO main" | sudo tee /etc/apt/sources.list.d/nodesource.list
echo "deb-src https://deb.nodesource.com/node_10.x $DISTRO main" | sudo tee -a /etc/apt/sources.list.d/nodesource.list

sudo apt-get update
sudo apt-get -y install software-properties-common php8.1 php8.1-common \
libapache2-mod-php8.1 php8.1-mysql php8.1-imagick php8.1-cli php8.1-curl \
php8.1-zip php8.1-gd php8.1-xml php8.1-mbstring php8.1-fpm php8.1-xmlrpc \
php8.1-imap php8.1-bcmath php8.1-opcache php8.1-soap php8.1-intl \
ffmpeg imagemagick git make g++ nodejs supervisor jq unzip php8.1-rdkafka

# Install AWS CLI
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# Install Composer
php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"
sudo php composer-setup.php --install-dir=/usr/bin --filename=composer
php -r "unlink('composer-setup.php');"

# Install Datadog PHP extension
curl -LO https://github.com/DataDog/dd-trace-php/releases/latest/download/datadog-setup.php
sudo php datadog-setup.php --php-bin=all --enable-appsec --enable-profiling
rm -f datadog-setup.php

# Thankview deployment setup
aws s3 cp s3://thankview-secrets/common/thankview_deploy /tmp/thankview_deploy
sudo chmod 0600 /tmp/thankview_deploy
export GIT_SSH_COMMAND='ssh -vv -i /tmp/thankview_deploy -o StrictHostKeyChecking=no'
sudo mkdir /var/www/thank-views
cd /var/www/thank-views
sudo chown ubuntu .
git config --global user.name "Michael Duncan"
git config --global user.email "<EMAIL>"
<NAME_EMAIL>:evertrue/ThankView-App.git .
sudo chown -R root .
sudo rm .env*