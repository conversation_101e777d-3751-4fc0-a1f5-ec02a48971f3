packer {
  required_plugins {
    amazon = {
      source  = "github.com/hashicorp/amazon"
      version = "~> 1"
    }
  }
}

# Commenting out the variables as they are not needed when using AWS_PROFILE
# variable "aws_access_key" {
#   type = string
# }

# variable "aws_secret_key" {
#   type = string
# }

variable "version" {
  type = string
}

locals { timestamp = regex_replace(timestamp(), "[- TZ:]", "") }

source "amazon-ebs" "autogenerated_1" {
  # Commenting out the following two lines as they are not needed when using AWS_PROFILE
  # access_key    = var.aws_access_key
  ami_name      = "thankview-video-worker-${var.version}"
  instance_type = "t3a.medium"
  region        = "us-east-1"
  # secret_key    = var.aws_secret_key
  source_ami_filter {
    filters = {
      name                = "ubuntu/images/hvm-ssd/ubuntu-focal-20.04-amd64-server-*"
      root-device-type    = "ebs"
      virtualization-type = "hvm"
    }
    most_recent = true
    owners      = ["099720109477"]
  }
  ssh_username         = "ubuntu"
  iam_instance_profile = "videoserver-prod"

  launch_block_device_mappings {
    device_name = "/dev/sda1"
    encrypted   = true
    volume_size = 32
    volume_type = "gp3"
  }
}

build {
  sources = ["source.amazon-ebs.autogenerated_1"]

  provisioner "file" {
    destination = "/tmp/nodesource.gpg.key"
    source      = "./config/nodesource.gpg.key"
  }
  provisioner "shell" {
    script = "./config/installs.sh"
  }
}
