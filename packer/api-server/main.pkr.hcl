packer {
  required_plugins {
    amazon = {
      source  = "github.com/hashicorp/amazon"
      version = "~> 1"
    }
  }
}

variable "version" {
  type = string
}

locals {
  timestamp = regex_replace(timestamp(), "[- TZ:]", "")
}

data "amazon-ami" "ubuntu" {
  filters = {
    name                = "ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-amd64-server-*"
    root-device-type    = "ebs"
    virtualization-type = "hvm"
  }
  most_recent = true
  owners      = ["099720109477"] # Canonical
  region      = "us-east-1"
}

source "amazon-ebs" "autogenerated_1" {
  ami_name             = "thankview-api-server-${var.version}"
  instance_type        = "t3a.medium"
  region               = "us-east-1"
  source_ami           = data.amazon-ami.ubuntu.id
  ssh_username         = "ubuntu"
  iam_instance_profile = "videoserver-prod"

  launch_block_device_mappings {
    device_name = "/dev/sda1"
    encrypted   = true
    volume_size = 32
    volume_type = "gp3"
  }
}

build {
  sources = ["source.amazon-ebs.autogenerated_1"]

  provisioner "file" {
    destination = "/tmp/nodesource.gpg.key"
    source      = "./config/nodesource.gpg.key"
  }

  # Install awscli via official script and retrieve SSH key from Parameter Store
  provisioner "shell" {
    inline = [
      "sudo apt update && sudo apt install -y unzip curl",
      "curl 'https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip' -o 'awscliv2.zip'",
      "unzip awscliv2.zip",
      "sudo ./aws/install",
      "sudo mkdir -p /home/<USER>/.ssh",
      "sudo /usr/local/bin/aws ssm get-parameter --name \"/packer_ami/github_deploy_key\" --with-decryption --region us-east-1 --query 'Parameter.Value' --output text > /home/<USER>/.ssh/id_rsa",
      "sudo chown ubuntu:ubuntu /home/<USER>/.ssh/id_rsa",
      "sudo chmod 600 /home/<USER>/.ssh/id_rsa",
      "ssh-keyscan github.com | sudo tee -a /home/<USER>/.ssh/known_hosts > /dev/null",
      "sudo chown ubuntu:ubuntu /home/<USER>/.ssh/known_hosts"
    ]
  }

  provisioner "shell" {
    script = "./config/installs.sh"
  }
}
