#!/bin/bash
set -e

echo "--- Updating apt and installing dependencies ---"
sudo apt-get update -y
sudo apt-get install -y software-properties-common curl gnupg2 ca-certificates lsb-release apt-transport-https wget zip unzip git apache2 mysql-client net-tools

echo "--- Adding PHP 8.1 repository from Sury ---"
sudo add-apt-repository ppa:ondrej/php -y
sudo apt-get update -y

echo "--- Installing PHP 8.1 and required extensions ---"
sudo apt-get install -y php8.1-cli php8.1-mbstring php8.1-xml php8.1-curl php8.1-bcmath php8.1-mysql php8.1-zip php8.1-gd libapache2-mod-php8.1

echo "--- Forcing Node.js 18.x setup using Jammy workaround ---"
curl -fsSL https://deb.nodesource.com/setup_18.x | sed 's/$(lsb_release -sc)/jammy/' | sudo -E bash -
sudo apt-get install -y nodejs

echo "--- Enabling Apache modules ---"
sudo a2enmod rewrite headers ssl
sudo service apache2 restart

echo "--- Setting global Git identity ---"
git config --global user.name "Zain Zubair"
git config --global user.email "<EMAIL>"

echo "--- Installing Composer ---"
curl -sS https://getcomposer.org/installer | sudo php -- --install-dir=/usr/local/bin --filename=composer

echo "--- Creating /var/www directory ---"
sudo mkdir -p /var/www
sudo chown ubuntu:ubuntu /var/www

echo "--- Cloning ThankView-API repo using SSH ---"
sudo -u ubuntu git clone --<NAME_EMAIL>:evertrue/ThankView-API.git /var/www/ThankView-API

echo "--- Fixing ownership ---"
sudo chown -R ubuntu:ubuntu /var/www/ThankView-API

echo "--- Cleaning up SSH key ---"
sudo rm -f /home/<USER>/.ssh/id_rsa
