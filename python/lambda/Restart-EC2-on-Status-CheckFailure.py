import boto3
from botocore.exceptions import Client<PERSON>rror

def lambda_handler(event, context):
    ec2 = boto3.client('ec2')

    # Extracting the alarm name from the CloudWatch event
    alarm_name = event['detail']['alarmName']

    # Extracting the instance ID from the alarm name
    instance_id = 'i-' + alarm_name.split('-i-')[-1]

    try:
        # Stopping the instance
        ec2.stop_instances(InstanceIds=[instance_id])
        print(f"Stopping instance: {instance_id}")

        # Wait for the instance to be stopped
        waiter = ec2.get_waiter('instance_stopped')
        waiter.wait(InstanceIds=[instance_id])
        print(f"Instance {instance_id} stopped.")

        # Starting the instance
        ec2.start_instances(InstanceIds=[instance_id])
        print(f"Started instance: {instance_id}")

    except ClientError as e:
        print(f"An error occurred: {e}")
        return {
            'statusCode': 500,
            'body': f"An error occurred: {e}"
        }

    return {
        'statusCode': 200,
        'body': f'Instance {instance_id} has been restarted'
    }
