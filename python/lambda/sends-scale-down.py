#Function checks minimum capacity required for auto scaling 
#Function checks desired capacity of auto scaling 
#Function will terminate newer created instances that above the minimum capacity requirement. 
#If minimum capacity = desired capacity no action is taken 

import boto3

def lambda_handler(event, context):
    asg_name = 'thankview-static-sends-servers'

    client = boto3.client('autoscaling')
    ec2 = boto3.client('ec2')

    # Get ASG information
    asg_info = client.describe_auto_scaling_groups(AutoScalingGroupNames=[asg_name])
    asg_details = asg_info['AutoScalingGroups'][0]

    # Read minimum capacity from ASG configuration
    min_capacity = asg_details['MinSize']
    current_capacity = len(asg_details['Instances'])

    # Proceed only if the current capacity is greater than minimum capacity
    if current_capacity > min_capacity:
        # Get EC2 instance information
        instance_ids = [instance['InstanceId'] for instance in asg_details['Instances']]
        ec2_instances = ec2.describe_instances(InstanceIds=instance_ids)
        instances = []
        for reservation in ec2_instances['Reservations']:
            for instance in reservation['Instances']:
                instances.append({
                    'InstanceId': instance['InstanceId'],
                    'LaunchTime': instance['LaunchTime']
                })

        # Sort instances by launch time (newest first)
        instances.sort(key=lambda x: x['LaunchTime'], reverse=True)

        # Terminate only the most recent instance
        most_recent_instance = instances[0]['InstanceId']
        ec2.terminate_instances(InstanceIds=[most_recent_instance])
        print(f"Terminating the most recent instance: {most_recent_instance}")

        # Set the desired capacity of the ASG to its minimum capacity
        client.update_auto_scaling_group(
            AutoScalingGroupName=asg_name,
            DesiredCapacity=min_capacity
        )
        print(f"Updated desired capacity of ASG to the minimum capacity: {min_capacity}")

    else:
        print("Scaling down not required. Current capacity is at or below minimum.")

    return {
        'statusCode': 200,
        'body': 'Scale down operation completed.'
    }
