locals {
  roles = {
    "ca-rds-nlb-lambda" = {
      create_role = true
      trusted_role_services = [
        "lambda.amazonaws.com"
      ]
      custom_role_policy_arns = [
        module.iam_policy["ca-rds-nlb-lambda"].arn
      ]
      number_of_custom_role_policy_arns = 1

    }
  }
}



module "iam_assumable_role" {
  source = "terraform-aws-modules/iam/aws//modules/iam-assumable-role"

  for_each = local.roles

  role_name         = try(each.key, "")
  create_role       = try(each.value.create_role, false)
  role_requires_mfa = try(each.value.role_requires_mfa, false)

  trusted_role_arns                 = try(each.value.trusted_role_arns, [])
  trusted_role_services             = try(each.value.trusted_role_services, [])
  custom_role_policy_arns           = try(each.value.custom_role_policy_arns, [])
  number_of_custom_role_policy_arns = try(each.value.number_of_custom_role_policy_arns, 0)
}
