locals {
  policies = {
    ca-rds-nlb-lambda = {
      path        = "/"
      description = "Lambda IAM Policy"

      policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents"
      ],
      "Resource": "arn:aws:logs:*:*:*",
      "Effect": "Allow"
    },
    {
      "Action": [
      "s3:PutObject",
      "s3:GetObject",
      "s3:DeleteObject",
      "s3:CreateBucket",
      "s3:DeleteBucket"
      ],
      "Resource": [
      "arn:aws:s3:::et-ca-nlb-rds-lambda-logs/*",
      "arn:aws:s3:::et-ca-nlb-rds-lambda-logs"
      ],
      "Effect": "Allow"
    },
    {
      "Action": [
      "s3:ListAllMyBuckets",
      "cloudwatch:PutMetricData",
      "elasticloadbalancing:RegisterTargets",
      "elasticloadbalancing:DeregisterTargets",
      "elasticloadbalancing:DescribeTargetHealth",
      "ec2:CreateNetworkInterface",
      "ec2:DescribeNetworkInterfaces",
      "ec2:DeleteNetworkInterface"
      ],
      "Resource": "*",
      "Effect": "Allow"
    }
  ]
}
EOF
    }
  }
}


module "iam_policy" {
  source   = "terraform-aws-modules/iam/aws//modules/iam-policy"
  for_each = local.policies

  name        = try(each.key, "")
  path        = "/"
  description = try(each.value.description, "")

  policy = try(each.value.policy, {})
}
