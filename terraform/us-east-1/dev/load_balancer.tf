resource "aws_lb" "webserver_dev" {
  name               = "webserver-dev"
  internal           = false
  load_balancer_type = "application"
  subnets            = data.aws_subnet_ids.default_subnets.ids
  security_groups    = [aws_security_group.webserver_load_balancer.id]
}

resource "aws_lb_target_group" "webserver_target" {
  name        = "webserver-dev"
  target_type = "instance"
  port        = 80
  protocol    = "HTTP"
  vpc_id      = data.aws_vpc.default.id

  health_check {
    interval = 20
    path     = "/api/status"
  }
}

resource "aws_lb_target_group_attachment" "webserver_target_association" {
  count            = length(aws_instance.webserver.*.id)
  target_group_arn = aws_lb_target_group.webserver_target.arn
  target_id        = aws_instance.webserver[count.index].id
}

resource "aws_lb_listener" "webserver_listener" {
  load_balancer_arn = aws_lb.webserver_dev.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type = "redirect"

    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_302"
    }
  }
}

data "aws_acm_certificate" "dev_thankview" {
  domain      = "tmp-dev.thankview.com"
  types       = ["AMAZON_ISSUED"]
  most_recent = true
}

resource "aws_lb_listener" "webserver_listener_https" {
  load_balancer_arn = aws_lb.webserver_dev.arn
  port              = "443"
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = data.aws_acm_certificate.dev_thankview.arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.webserver_target.arn
  }
}
