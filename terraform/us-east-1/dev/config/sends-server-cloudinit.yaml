#cloud-config

# Cloud config examples at https://cloudinit.readthedocs.io/en/latest/topics/examples.html

users:
  - name: ubuntu
    sudo: ALL=(ALL) NOPASSWD:ALL
    ssh_authorized_keys:
      - ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIEw/2C6JFd6gdrj6/6G+EIfVqqbde3pFkcoQn5V6gKlz alex@pop-os
      - ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDfD2ORM6LzQC4Wfp/+MCb10FFtx83Gw2n5hFMm+iZFm94MmEOuYl/xKS6Eke2tHFU8s9x9FUGkYo/Nzi5oYFje0fez1Fu7vMwhja98opupCWU0RkuzesPnoY2CuEEBl7nFZh7avqvJFZzkcz2q7aisG/FKYjbzTlt2JH51watlzkGDOy9bYzqdSUVvuZ3chICvJA6Vf2UOtBEky44gQ+ckuoS5Ka7B+TcB1FTl9BQGnQTyfz4HGP65Avj+bkE+ei5p4KXxbMpTz65P5FvPuVQOUajPv12OS1q+5sHRtOjt4Hqr+dTvNwe5qUSdQSxGjg4/Z9nYwSfamMyL2+gMM9YR <EMAIL>
      - ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDvQicdlNad+NgFEjzpmjh91ZlK6BHHx/+Xdajm9xE4Qjr1RcunQ9vJMDWMG549bsfDb3iNg1m5XxHtFSOgX3M9iadrlRnO6w+s83G6lVn4Ce54f2hxhZpLat8A9BAru/CDhA7egm1MCFnBxJFPmIOoeVIyZG0sHZ4wnzr6JDGdo/OyCcBs7KEvrIJK8aDfcC/FCM6bD7TXkPGPbfnl5MaOAm70j4QiyYZ2zcFPoXmjcF8vhYA//0qFnMx099ErtKhzJr+B+54TxKAO0ElQ7V65YkwN5ZOgFiWQxQlyjeVHX7j12Jfifx1kfmgaQpV8Nw2C/S96TctY/T2fFSH38gjZ
      - ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDX3fMsliRmznlc3KNbs4eOrQ7jDN0kVW3wqDs9C4CkrADsQcZOytWK6O0EjismuI7BV0UqHUR6zSq2rLIKsdL6I4rNm1BgI/SQ7z9JqlXMkSihEhIpGUwuH6u3NQyMeXo9MslHY1x2siJO0dcnbjCpbbkvf1sIu6wgn5Z4kZ1eGZ4yZoXll5pwa3vHp5j8TXvz7lvcY6CivrBzUhnyDzIcLdGfsVCoQv+QqGStxJ2yx94ZZyluRed9J6MoWMJFdo98ZdFutZ+ValF1N4MWZWl0vopfNX7M4+JMVSf2Taoe4yxbK2kc4ttPuZ7tudRhEwZjuSpiew5t5UE6j7GCCeGWEKfLbFG+IStVahZ8tyra+ZucEi5QWZLcdEKuI9j5LLqOXuRGO0fWjBTSuEw/n/zm8Iezd0wEs7ij2zCVGsnGyN69n46JVMq1auF36OckXNyK9VY1AvugNjMOx6lIP40WagDVsaeCqObaBWXiUtcJOFF2Og85RBOvtxwyPEblQPM= robertkatz@Roberts-MBP
    shell: /bin/bash

runcmd:
  - aws s3 cp s3://thankview-secrets/common/thankview_deploy /root/thankview_deploy
  - chmod 0600 /root/thankview_deploy
  - export GIT_SSH_COMMAND='ssh -i /root/thankview_deploy -o StrictHostKeyChecking=no'
  - cd /var/www/thank-views
  - git checkout origin/production
  - git pull
  - aws ssm get-parameter --name "//thankview-ps/dev/sends-server" --with-decryption --output text | cut -d$'\t' -f7 > .env
  - chown -R ubuntu:ubuntu /var/www
  - find /var/www -type f -exec chmod 644 {} \;
  - find /var/www -type d -exec chmod 775 {} \;
  - chgrp -R ubuntu /var/www/thank-views/storage /var/www/thank-views/bootstrap /var/www/thank-views/vendor
  - chmod -R ug+rwx /var/www/thank-views/storage /var/www/thank-views/bootstrap /var/www/thank-views/vendor
  - sudo -H -u ubuntu composer install --no-dev
  - sudo -H -u ubuntu composer dump-autoload
  - sudo -H -u ubuntu php artisan clear-compiled
  - systemctl restart apache2

write_files:
  - encoding: b64
    content: ${digicert_ca_encoded}
    owner: root:root
    path: /etc/ssl/DigiCertCA.crt
    permissions: '0644'

  - encoding: b64
    content: ${elasticsearch_ca_encoded}
    owner: root:root
    path: /etc/ssl/elasticsearch-ca.pem
    permissions: '0644'
  
  - owner: root:root
    path: /etc/cron.d/thankview-schedule
    permissions: '0755'
    content: |
      * * * * * ubuntu php /var/www/thank-views/artisan schedule:run >> /dev/null 2>&1
