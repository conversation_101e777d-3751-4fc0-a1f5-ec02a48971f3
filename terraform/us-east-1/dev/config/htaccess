#AuthType Basic
#AuthName "Password Protected Area"
#AuthUserFile /var/password/.htpasswd
#Require valid-user

<IfModule mod_rewrite.c>
    Options -MultiViews
    
    RewriteEngine On
    
    # Redirect Trailing Slashes. Remember to redirect to https
    RewriteRule ^(.*)/$ https://%{HTTP_HOST}/$1 [L,R=302]
    
    # Handle Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Headers "Api-Gateway-Token, Content-Type"
    Header set Access-Control-Allow-Methods "DELETE, POST, GET, OPTIONS"
    Header set Access-Control-Allow-Credentials 'true'
</IfModule>

