<VirtualHost *:80>
    ServerName tmp-dev.thankview.com
    DocumentRoot /var/www/thank-views/public

    <Directory "/var/www/thank-views/public">
        Options Indexes FollowSymLinks MultiViews
        AllowOverride All
        Order allow,deny
        allow from all
    </Directory>
</VirtualHost>
<VirtualHost *:80>
   ServerName beebesbuttcamp.thankview.com
   ServerAlias *.thankview.com

   RewriteEngine On
   RewriteCond %{HTTP_HOST} ^(.+)\.thankview\.com$
   RewriteRule ^(.*)$ https://%1.thankview.com/$1 [R=302,L]
</VirtualHost>

#<VirtualHost *:80>
#    ServerName thankview.com
#    Redirect permanent / https://thankview.com/
#</VirtualHost>
