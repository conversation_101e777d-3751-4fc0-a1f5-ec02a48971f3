data "aws_ami" "amazon_linux" {
  most_recent = true
  owners = ["amazon"]

  filter {
    name   = "owner-alias"
    values = ["amazon"]
  }

  filter {
    name   = "name"
    values = ["amzn2-ami-hvm*"]
  }
}

module "bastion_host" {
  source                      = "terraform-aws-modules/ec2-instance/aws"
  ami                         = data.aws_ami.amazon_linux.id
  name                        = "dev-bastion-host"
  instance_type               = "t2.micro"
  iam_instance_profile        = "thankview-ssm-ec2-instance-role"
  key_name                    = "thankview-dev-ec2"
  subnet_ids                  = data.aws_subnet_ids.default_subnets.ids
  vpc_security_group_ids      = [aws_security_group.bastion_host_sg.id]
  root_block_device = [
    {
      volume_type = "gp2"
      volume_size = 10
    },
  ]
}