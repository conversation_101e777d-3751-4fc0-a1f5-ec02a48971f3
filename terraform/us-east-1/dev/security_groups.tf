resource "aws_security_group" "webserver" {
  name        = "webserver-dev"
  description = "Access needed for thankview webservers"
  vpc_id      = data.aws_vpc.default.id

  ingress {
    description     = "HTTP access from load balancer"
    from_port       = 80
    to_port         = 80
    protocol        = "tcp"
    security_groups = [aws_security_group.webserver_load_balancer.id]
  }

  ingress {
    description = "Global SSH access"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_security_group" "webserver_load_balancer" {
  name        = "webserver-lb-dev"
  description = "Access needed for thankview load balancer"
  vpc_id      = data.aws_vpc.default.id

  ingress {
    description = "Global HTTP"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    description = "Global HTTPS"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_security_group" "webserver_mysql_access" {
  name        = "webserver-mysql-dev"
  description = "Access needed for thankview database"
  vpc_id      = data.aws_vpc.default.id

  ingress {
    description     = "Bastion Host MySQL Access"
    from_port       = 6379
    to_port         = 6379
    protocol        = "tcp"
    security_groups = [aws_security_group.bastion_host_sg.id]
  }

  ingress {
    description     = "Webserver mysql access"
    from_port       = 3306
    to_port         = 3306
    protocol        = "tcp"
    security_groups = [aws_security_group.webserver.id]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_security_group" "webserver_redis_access" {
  name        = "webserver-redis-dev"
  description = "Access needed for thankview database"
  vpc_id      = data.aws_vpc.default.id

  ingress {
    description     = "Webserver mysql access"
    from_port       = 6379
    to_port         = 6379
    protocol        = "tcp"
    security_groups = [aws_security_group.webserver.id]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_security_group" "bastion_host_sg" {
  name        = "bastion-host-dev"
  description = "Access from bastion host instance"
  vpc_id      = data.aws_vpc.default.id

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}
