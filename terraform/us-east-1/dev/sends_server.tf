data "aws_ami" "sends_server" {
  most_recent = true

  filter {
    name   = "name"
    values = ["thankview-sends-server-*"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }

  owners = ["019190539304"]
}

data "aws_iam_policy_document" "sendsserver_sqs_access" {
  statement {
    actions = [
      "sqs:ReceiveMessage",
      "sqs:DeleteMessage"
    ]
    resources = [
      module.mail_sqs_queue.sqs_queue_arn
    ]
  }
}

resource "aws_iam_role" "sendsserver" {
  name               = "sends-server-role-dev"
  assume_role_policy = data.aws_iam_policy_document.webserver_assume_role.json
  managed_policy_arns = [ 
    "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore",
    "arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy"
  ]

  inline_policy {
    name = "sendsserver-sqs-access"
    policy = data.aws_iam_policy_document.sendsserver_sqs_access.json
  }

  inline_policy {
    name = "sendsserver-secrets-access"
    policy = data.aws_iam_policy_document.webserver_secrets_access.json
  }
}

resource "aws_iam_instance_profile" "sendsserver" {
  name = "sendsserver-dev"
  role = aws_iam_role.sendsserver.name
}

module "sends_server" {
  source                      = "terraform-aws-modules/ec2-instance/aws"
  ami                         = data.aws_ami.sends_server.id
  name                        = "dev-sends-server"
  instance_type               = "t3a.medium"
  iam_instance_profile        = aws_iam_instance_profile.sendsserver.name
  key_name                    = "thankview-dev-ec2"
  subnet_ids                  = data.aws_subnet_ids.default_subnets.ids
  vpc_security_group_ids      = [aws_security_group.bastion_host_sg.id]

  user_data = templatefile(
    "./config/sends-server-cloudinit.yaml",
    {
      digicert_ca_encoded                  = filebase64("./config/DigiCertSHA2SecureServerCA.crt.pem")
      elasticsearch_ca_encoded             = filebase64("./config/elasticsearch-ca.pem")
    }
  )

  root_block_device = [
    {
      volume_type = "gp2"
      volume_size = 80
    },
  ]
}