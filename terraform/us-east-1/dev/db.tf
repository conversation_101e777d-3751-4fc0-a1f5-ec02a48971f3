resource "random_password" "db_pass" {
  length = 10
}

resource "aws_db_instance" "thankview_db" {
  allocated_storage = 30
  storage_type      = "gp2"
  engine            = "mysql"
  engine_version    = "5.7.31"
  instance_class    = "db.t2.micro"
  identifier        = "thankview-dev"
  name              = "thankview"
  username          = "thankview"
  password          = random_password.db_pass.result

  skip_final_snapshot = true

  vpc_security_group_ids = [aws_security_group.webserver_mysql_access.id]
}
