resource "random_password" "cache_pass" {
  length           = 20
  override_special = "!&#$^<>-"
}

resource "aws_elasticache_replication_group" "thankview_cache" {
  replication_group_id          = "thankview-dev"
  replication_group_description = "Thankview dev redis"
  availability_zones            = ["us-east-1a"]
  engine                        = "redis"
  engine_version                = "4.0.10"
  parameter_group_name          = "default.redis4.0"
  node_type                     = "cache.t3.medium"
  automatic_failover_enabled    = false
  number_cache_clusters         = 1
  port                          = 6379
  transit_encryption_enabled    = false
  security_group_ids            = [aws_security_group.webserver_redis_access.id]
}
