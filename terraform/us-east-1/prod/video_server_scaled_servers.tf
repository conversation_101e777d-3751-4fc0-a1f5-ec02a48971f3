resource "aws_launch_template" "video_scaled_conf" {
  name_prefix            = "thankview-scaled-video-worker-"
  image_id               = data.aws_ami.video_server.id
  instance_type          = "c6a.2xlarge"
  ebs_optimized          = true
  update_default_version = true


  iam_instance_profile {
    name = aws_iam_instance_profile.videoserver.name
  }
  vpc_security_group_ids = [aws_security_group.video-server.id, aws_security_group.thankview-ssh-access.id]

  user_data = base64encode(templatefile(
    "./config/video-worker-cloudinit-scaled.yaml",
    {
      digicert_ca_encoded                  = filebase64("./config/DigiCertSHA2SecureServerCA.crt.pem")
      elasticsearch_ca_encoded             = filebase64("./config/elasticsearch-ca.pem")
      video_worker_supervisor_conf_encoded = filebase64("./config/video-worker-supervisor.conf")
      datadog_api_key                      = data.aws_ssm_parameter.datadog_api_key.value #datadog SSM parameter value
    }
  ))

  block_device_mappings {
    device_name = "/dev/sda1"
    ebs {
      volume_size           = 32    # Match this to your AMI's volume size if you want to keep it
      delete_on_termination = true  # Set to true to ensure the volume is deleted upon instance termination
      volume_type           = "gp3" # Specify your desired volume type, matching AMI or as needed
      encrypted             = true  # Set according to your encryption needs
    }
  }

  tag_specifications {
    resource_type = "instance"
    tags = {
      Name             = "video-worker-prod-scaled",
      VantaDescription = "Video worker - scaled",
      VantaOwner       = "<EMAIL>",
      VantaProd        = "true"
      Team             = "devops"
      Service          = "video-worker"
      Environment      = "prod"
      APP_ROLE         = "video-worker"
    }
  }

  tag_specifications {
    resource_type = "volume"
    tags = {
      Name = "video-worker-prod-scaled"
    }
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_autoscaling_group" "video_autoscaling" {
  name     = "thankview-scaling-video-workers"
  min_size = 0
  max_size = 20
  availability_zones = [
    "us-east-1a",
    "us-east-1b",
    "us-east-1c"
  ]
  enabled_metrics = [
    "GroupAndWarmPoolDesiredCapacity",
    "GroupAndWarmPoolTotalCapacity",
    "GroupDesiredCapacity",
    "GroupInServiceCapacity",
    "GroupInServiceInstances",
    "GroupMaxSize",
    "GroupMinSize",
    "GroupPendingCapacity",
    "GroupPendingInstances",
    "GroupStandbyCapacity",
    "GroupStandbyInstances",
    "GroupTerminatingCapacity",
    "GroupTerminatingInstances",
    "GroupTotalCapacity",
    "GroupTotalInstances",
    "WarmPoolDesiredCapacity",
    "WarmPoolMinSize",
    "WarmPoolPendingCapacity",
    "WarmPoolTerminatingCapacity",
    "WarmPoolTotalCapacity",
    "WarmPoolWarmedCapacity",
  ]

  launch_template {
    id      = aws_launch_template.video_scaled_conf.id
    version = "$Latest"
  }

  instance_refresh {
    strategy = "Rolling"
    preferences {
      instance_warmup = 120
    }
  }

  lifecycle {
    create_before_destroy = true
    ignore_changes = [
      desired_capacity
    ]
  }
}

resource "aws_autoscaling_schedule" "scale_out" {
  scheduled_action_name  = "Scale_to_20"
  desired_capacity       = 2
  min_size               = 0
  max_size               = 20
  time_zone              = "Etc/UTC"
  start_time             = "2023-08-29T11:00:00Z"
  recurrence             = "0 11 * * *"
  autoscaling_group_name = aws_autoscaling_group.video_autoscaling.name

  lifecycle {
    ignore_changes = [start_time]
  }
}

resource "aws_autoscaling_schedule" "scale_in" {
  scheduled_action_name  = "Scale_to_0"
  desired_capacity       = 0
  min_size               = 0
  max_size               = 0
  time_zone              = "Etc/UTC"
  start_time             = "2023-08-29T02:00:00Z"
  recurrence             = "0 2 * * *"
  autoscaling_group_name = aws_autoscaling_group.video_autoscaling.name

  lifecycle {
    ignore_changes = [start_time]
  }
}


resource "aws_autoscaling_policy" "simple_scaling_policy" {
  name                   = "CloudWatch alarm"
  scaling_adjustment     = 2
  adjustment_type        = "ChangeInCapacity"
  cooldown              = 600
  autoscaling_group_name = "thankview-scaling-video-workers"
  policy_type           = "SimpleScaling"
}
