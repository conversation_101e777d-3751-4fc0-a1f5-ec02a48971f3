data "aws_vpc" "default" {
  default = true
}

data "aws_subnets" "default_subnets" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.default.id]
  }
}

resource "random_id" "index" {
  byte_length = 2
}

# Retrieve the Datadog API key securely from AWS Systems Manager Parameter Store
data "aws_ssm_parameter" "datadog_api_key" {
  name = "/datadog/api_key"
}

data "aws_sns_topic" "existing_topic" {
  name = "alarm-monitoring"
}

data "aws_vpc" "thankview_vpc" {
  filter {
    name   = "tag:Name"
    values = ["prod"]
  }
}