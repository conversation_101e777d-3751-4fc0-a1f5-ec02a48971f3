resource "aws_lb" "webserver_prod" {
  name               = "webserver-prod"
  internal           = false
  load_balancer_type = "application"
  #subnets            = data.aws_subnet_ids.default_subnets.ids
  subnets         = data.aws_subnets.default_subnets.ids
  security_groups = [aws_security_group.webserver_load_balancer.id]
  idle_timeout    = 120
}

resource "aws_lb_target_group" "webserver_target" {
  name        = "webserver-prod"
  target_type = "instance"
  port        = 80
  protocol    = "HTTP"
  vpc_id      = data.aws_vpc.default.id

  health_check {
    interval = 20
    path     = "/api/status"
  }

  stickiness {
    type    = "lb_cookie"
    enabled = true
  }
}

resource "aws_lb_target_group_attachment" "webserver_target_association" {
  count            = length(aws_instance.webserver.*.id)
  target_group_arn = aws_lb_target_group.webserver_target.arn
  target_id        = aws_instance.webserver[count.index].id
}

resource "aws_lb_listener" "webserver_listener" {
  load_balancer_arn = aws_lb.webserver_prod.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type = "redirect"

    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_302"
    }
  }
}

data "aws_acm_certificate" "prod_thankview" {
  domain      = "*.thankview.com"
  types       = ["AMAZON_ISSUED"]
  most_recent = true
}

resource "aws_lb_listener" "webserver_listener_https" {
  load_balancer_arn = aws_lb.webserver_prod.arn
  port              = "443"
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-TLS-1-2-Ext-2018-06"
  certificate_arn   = "arn:aws:acm:us-east-1:019190539304:certificate/a6a3e27c-f6d4-4014-a95b-199898f506b6"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.webserver_target.arn
  }
}

resource "aws_lb_listener_rule" "status_redirect" {
  listener_arn = aws_lb_listener.webserver_listener_https.arn
  priority     = 10 # Assign an appropriate priority number

  condition {
    host_header {
      values = ["status.thankview.com"]
    }
  }

  action {
    type = "redirect"
    redirect {
      host        = "status.evertrue.com"
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301" # Permanent redirect
    }
  }
}
