data "aws_ami" "amazon_linux" {
  most_recent = true
  owners      = ["amazon"]

  filter {
    name   = "owner-alias"
    values = ["amazon"]
  }

  filter {
    name   = "name"
    values = ["amzn2-ami-hvm*"]
  }
}

module "bastion_host" {
  source                 = "terraform-aws-modules/ec2-instance/aws"
  version                = "5.5.0"
  ami                    = "ami-0c2b8ca1dad447f8a"
  name                   = "prod-bastion-host"
  instance_type          = "t2.micro"
  iam_instance_profile   = "thankview-ssm-ec2-instance-role"
  key_name               = "thankview-prod-ec2"
  subnet_id              = "subnet-17a59f4e"
  vpc_security_group_ids = [aws_security_group.bastion_host_sg.id]
  root_block_device = [
    {
      volume_type = "gp3"
      volume_size = 10
    },
  ]
  tags = {
    DataDog      = "true"
    Environment  = "prod-1"
    Service      = "infrastructure"
    Team         = "devops"
    VantaNoAlert = "This is a temporary test environment."
    VantaNonProd = "true"
  }
}
