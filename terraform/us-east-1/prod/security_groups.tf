# Local variables for organizing IP addresses
locals {
  # User IP addresses for MySQL readonly access
  mysql_readonly_user_ips = {
    "Joe IP address"                = "************/32"
    "<PERSON><PERSON><PERSON><PERSON> IP address"          = "**************/32"
    "Torrin <PERSON>le IP"           = "*************/32"
    "<PERSON> IP"              = "*************/32"
    "<PERSON> Fitzgerald IP address" = "**************/32"
    "<PERSON>"                  = "*************/32"
    "<PERSON>"                 = "**************/32"
    "Ken IP Address"               = "**************/32"
    "WeWork Boston 2024-12-17"     = "***********/32"
    "doug youch ip address"        = "*************/32"
    "jerry ip address"             = "************/32"
    "yee ip address"               = "************/32"
    "<PERSON>"                = "**************/32"
    "<PERSON>"               = "**************/32"
    "<PERSON> <PERSON><PERSON>h IP"                   = "**************/32"
    "<PERSON>"                   = "***************/32"
    "<PERSON> temp"              = "***************/32"
    "Coronado IP"                  = "**************/32"
    "Zain IP"                      = "*************/32"
    "<PERSON> IP address"    = "**************/32"
  }

  # Klipfolio service IP addresses for MySQL readonly access
  mysql_readonly_klipfolio_ips = {
    "Klipfolio address 1" = "*************/32"
    "Klipfo<PERSON> address 2" = "*************/32"
    "Klipfolio address 3" = "*************/32"
    "Klipfolio address 4" = "*************/32"
    "Klipfolio address 5" = "************/32"
    "Klipfolio address 6" = "************/32"
    "Klipfolio address 7" = "***********/32"
    "Klipfolio address 8" = "*************/32"
    "Klipfolio address 9" = "*************/32"
  }
}

resource "aws_security_group" "webserver" {
  name        = "webserver-prod"
  description = "Access needed for thankview webservers"
  vpc_id      = data.aws_vpc.default.id

  ingress {
    description     = "HTTP access from load balancer"
    from_port       = 80
    to_port         = 80
    protocol        = "tcp"
    security_groups = [aws_security_group.webserver_load_balancer.id]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_security_group" "webserver_load_balancer" {
  name        = "webserver-lb-prod"
  description = "Access needed for thankview load balancer"
  vpc_id      = data.aws_vpc.default.id

  ingress {
    description = "Global HTTP"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    description = "Global HTTPS"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_security_group" "video-server" {
  name        = "video-server"
  description = "Access to and from video server"
  vpc_id      = data.aws_vpc.default.id

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_security_group" "sends-server" {
  name        = "sends-server"
  description = "Access to and from sends server"
  vpc_id      = data.aws_vpc.default.id

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_security_group" "thankview-ssh-access" {
  name        = "thankview-ssh-access"
  description = "Allows SSH access to developers"
  vpc_id      = data.aws_vpc.default.id


  ingress {
    description = "github runner"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "Michelle H temp"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["***************/32"]
  }

   ingress {
    description = "Michael IP address"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["**************/32"]
  }

  
  ingress {
    description = "Yee IP address"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "Doug IP address"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }
  ingress {
    description = "Jerry IP address"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "Joe IP address"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "test icmp from shared services prod"
    from_port   = -1
    to_port     = -1
    protocol    = "icmp"
    cidr_blocks = ["*********/16"]
  }


  ingress {
    description = "deployment server public ip"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "deployment server"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description     = "allow all traffic from deployment server sg"
    from_port       = 0
    to_port         = 0
    protocol        = "-1"
    security_groups = ["sg-0a3710c9b066a9f39"]
  }

  ingress {
    description = "Coronado IP address"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["**************/32"]
  }

  ingress {
    description = "Coronado IP address"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["**************/32"]
  }

}


resource "aws_security_group" "webserver_mysql_access" {
  name        = "webserver-mysql-prod"
  description = "Access needed for thankview database"
  vpc_id      = data.aws_vpc.default.id

  ingress {
    description     = "Webserver mysql access"
    from_port       = 3306
    to_port         = 3306
    protocol        = "tcp"
    security_groups = [aws_security_group.webserver.id]
  }

  ingress {
    description     = "Allow Video Server ASG instances"
    from_port       = 3306
    to_port         = 3306
    protocol        = "tcp"
    security_groups = [aws_security_group.video-server.id]
  }

  ingress {
    description     = "Allow Send Server ASG instances"
    from_port       = 3306
    to_port         = 3306
    protocol        = "tcp"
    security_groups = [aws_security_group.sends-server.id]
  }

  ingress {
    description     = "SFTP acesss"
    from_port       = 3306
    to_port         = 3306
    protocol        = "tcp"
    security_groups = ["sg-055c5d57c7cdd892b"]
  }

  ingress {
    description = "Media 3 Server"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "Media 5 Server"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description = "Media 4 Server"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description = "Secure Server"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["**********/32", "*************/32"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_security_group" "webserver_mysql_access_readonly" {
  name        = "webserver-mysql-prod-readonly"
  description = "Readonly access to mysql prod"
  vpc_id      = data.aws_vpc.default.id

  ingress {
    description     = "Bastion Host MySQL Access"
    from_port       = 3306
    to_port         = 3306
    protocol        = "tcp"
    security_groups = [aws_security_group.bastion_host_sg.id]
  }

  ingress {
    description     = "deployment-server"
    from_port       = 3306
    to_port         = 3306
    protocol        = "tcp"
    security_groups = [aws_security_group.deployment_server_sg.id]
  }

  ingress {
    description     = "DMS task"
    from_port       = 3306
    to_port         = 3306
    protocol        = "tcp"
    security_groups = ["sg-0307c6344e7b8e48a"]
  }
}

# Security group rules for user IP addresses
resource "aws_security_group_rule" "mysql_readonly_user_access" {
  for_each = local.mysql_readonly_user_ips

  type              = "ingress"
  description       = each.key
  from_port         = 3306
  to_port           = 3306
  protocol          = "tcp"
  cidr_blocks       = [each.value]
  security_group_id = aws_security_group.webserver_mysql_access_readonly.id
}

# Security group rules for Klipfolio service IP addresses
resource "aws_security_group_rule" "mysql_readonly_klipfolio_access" {
  for_each = local.mysql_readonly_klipfolio_ips

  type              = "ingress"
  description       = each.key
  from_port         = 3306
  to_port           = 3306
  protocol          = "tcp"
  cidr_blocks       = [each.value]
  security_group_id = aws_security_group.webserver_mysql_access_readonly.id
}




resource "aws_security_group" "webserver_redis_access" {
  name        = "webserver-redis-prod"
  description = "Access needed for thankview database"
  vpc_id      = data.aws_vpc.default.id

  ingress {
    description = "Media 3 Server"
    from_port   = 6379
    to_port     = 6379
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "Secure Server"
    from_port   = 6379
    to_port     = 6379
    protocol    = "tcp"
    cidr_blocks = ["*************/32", "************/32"]
  }

  ingress {
    description = "SFTP acesss"
    from_port   = 6379
    to_port     = 6379
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "SFTP CA access"
    from_port   = 6379
    to_port     = 6379
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "Allows access to secure-us1.thankview.com in us-east-2"
    from_port   = 6379
    to_port     = 6379
    protocol    = "tcp"
    cidr_blocks = ["**********/32"]
  }

  ingress {
    description = "CA Secure Host"
    from_port   = 6379
    to_port     = 6379
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description = "Media 5 Server"
    from_port   = 6379
    to_port     = 6379
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description = "Media 4 Server"
    from_port   = 6379
    to_port     = 6379
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description     = "Allow Send Server ASG instances"
    from_port       = 6379
    to_port         = 6379
    protocol        = "tcp"
    security_groups = [aws_security_group.sends-server.id]
  }

  ingress {
    description     = "Allow Video Server ASG instances"
    from_port       = 6379
    to_port         = 6379
    protocol        = "tcp"
    security_groups = [aws_security_group.video-server.id]
  }

  ingress {
    description     = "web servers"
    from_port       = 6379
    to_port         = 6379
    protocol        = "tcp"
    security_groups = [aws_security_group.webserver.id]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_security_group" "bastion_host_sg" {
  name        = "bastion-host-prod"
  description = "Access from bastion host instance"
  vpc_id      = data.aws_vpc.default.id

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}


resource "aws_security_group" "deployment_server_sg" {
  name        = "deployment_server_sg"
  description = "allows access from all resources in VPC"
  vpc_id      = data.aws_vpc.default.id

  ingress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["**********/16"]
  }

  ingress {
    description = "media3"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "secure-us1.thankview.com"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["**********/32"]
  }

  ingress {
    description = "media5"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description = "secure-ca1.thankview.com"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description = "prod-ca-sftp-host"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "prod-ca-api-host"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["**********/32"]
  }

  ingress {
    description     = "Allow all traffic from thankview ssh sg"
    from_port       = 0
    to_port         = 0
    protocol        = "-1"
    security_groups = ["sg-0fc2847dcef594565"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "deployment-server-sg"
  }

}