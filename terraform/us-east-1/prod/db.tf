resource "random_password" "db_pass" {
  length = 10
}

resource "aws_db_instance" "thankview_db" {
  identifier                   = "thankview-prod"
  db_name                      = "thankview"
  engine                       = "mysql"
  engine_version               = "8.0.40"
  instance_class               = "db.m5.2xlarge"
  allocated_storage            = 200
  max_allocated_storage        = 1000
  storage_type                 = "gp3"
  storage_encrypted            = true
  username                     = "thankview"
  password                     = random_password.db_pass.result
  monitoring_interval          = 60
  performance_insights_enabled = true
  deletion_protection          = true

  // Creates a multi-availability zone fallover.
  multi_az            = true
  publicly_accessible = true

  // Backup related
  backup_retention_period = 31
  backup_window           = "02:30-04:30"

  vpc_security_group_ids = [
    aws_security_group.webserver_mysql_access.id,
    aws_security_group.webserver_mysql_access_readonly.id,
  ]

  tags = {
    Name = "thankview-prod"
  }



  lifecycle {
    prevent_destroy = true
    ignore_changes = [
      password
    ]
  }
}
