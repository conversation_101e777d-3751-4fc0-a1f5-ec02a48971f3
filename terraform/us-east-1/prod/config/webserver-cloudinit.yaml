#cloud-config

# Cloud config examples at https://cloudinit.readthedocs.io/en/latest/topics/examples.html

users:
  - name: ubuntu
    sudo: ALL=(ALL) NOPASSWD:ALL
    ssh_authorized_keys:
      - ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDfD2ORM6LzQC4Wfp/+MCb10FFtx83Gw2n5hFMm+iZFm94MmEOuYl/xKS6Eke2tHFU8s9x9FUGkYo/Nzi5oYFje0fez1Fu7vMwhja98opupCWU0RkuzesPnoY2CuEEBl7nFZh7avqvJFZzkcz2q7aisG/FKYjbzTlt2JH51watlzkGDOy9bYzqdSUVvuZ3chICvJA6Vf2UOtBEky44gQ+ckuoS5Ka7B+TcB1FTl9BQGnQTyfz4HGP65Avj+bkE+ei5p4KXxbMpTz65P5FvPuVQOUajPv12OS1q+5sHRtOjt4Hqr+dTvNwe5qUSdQSxGjg4/Z9nYwSfamMyL2+gMM9YR <EMAIL>
      - ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDvQicdlNad+NgFEjzpmjh91ZlK6BHHx/+Xdajm9xE4Qjr1RcunQ9vJMDWMG549bsfDb3iNg1m5XxHtFSOgX3M9iadrlRnO6w+s83G6lVn4Ce54f2hxhZpLat8A9BAru/CDhA7egm1MCFnBxJFPmIOoeVIyZG0sHZ4wnzr6JDGdo/OyCcBs7KEvrIJK8aDfcC/FCM6bD7TXkPGPbfnl5MaOAm70j4QiyYZ2zcFPoXmjcF8vhYA//0qFnMx099ErtKhzJr+B+54TxKAO0ElQ7V65YkwN5ZOgFiWQxQlyjeVHX7j12Jfifx1kfmgaQpV8Nw2C/S96TctY/T2fFSH38gjZ
      - ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDX3fMsliRmznlc3KNbs4eOrQ7jDN0kVW3wqDs9C4CkrADsQcZOytWK6O0EjismuI7BV0UqHUR6zSq2rLIKsdL6I4rNm1BgI/SQ7z9JqlXMkSihEhIpGUwuH6u3NQyMeXo9MslHY1x2siJO0dcnbjCpbbkvf1sIu6wgn5Z4kZ1eGZ4yZoXll5pwa3vHp5j8TXvz7lvcY6CivrBzUhnyDzIcLdGfsVCoQv+QqGStxJ2yx94ZZyluRed9J6MoWMJFdo98ZdFutZ+ValF1N4MWZWl0vopfNX7M4+JMVSf2Taoe4yxbK2kc4ttPuZ7tudRhEwZjuSpiew5t5UE6j7GCCeGWEKfLbFG+IStVahZ8tyra+ZucEi5QWZLcdEKuI9j5LLqOXuRGO0fWjBTSuEw/n/zm8Iezd0wEs7ij2zCVGsnGyN69n46JVMq1auF36OckXNyK9VY1AvugNjMOx6lIP40WagDVsaeCqObaBWXiUtcJOFF2Og85RBOvtxwyPEblQPM= robertkatz@Roberts-MBP
      - ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQCRa5Dfs6+l9c4KxJDHHqj3QQ8IDGuFn1+FKg3QhL2iC3jU04Vb9NkMlkXmvTdnWy7SySwSaCGlj3iwZsLD9KBznmnvDZ1st6WLxcGIJCN6rKld3JV4nQnO6J8rP3WbjLprRAUvVgZbiBhm9st7fOYkjBAdsLx9IwjhHlVHFoHVNwMrS4QYVjkHRFVHBuMYSWpCNt1TtM7wuVaQBk756UlQ0SqBoo3d4DHbgOnNrUMb5U8laZq10gQ+0RFKCUIoBVCkoQazAHlpdAT/L7ANeSHniQ4Zi9epwSsjyv56FuXW2Ou59JJSvpCUPmqazTCyRFEUc7AAVZytqqDBZuTZfReWZ1EC6NNYemwqQ6HnvtS9VkoPsIeUj4SSMxFYaLvXcUymwQgmj3kHDxBDTCtT1VRecicyiZQvmgSJ+iFlcZb6br/S6kT9EWCY1WPs9dvHyf5392xa/CuEPapOGj90nR4g5jOKdSJtYOiAwJsWSQ3uqU2GpFsMqIWTtZJi4nzN9+RmLh6Yp5RGWG6GRf+P0YWJFr8G+o+q7IjrZuD6WFKRlXcF4zYjMSF/YWGw0Lmzz242Gq1ArAlwtK3nrP0Vb/mHOVLgTFZwoH/LsGH3Av26L6V+Kl1Ist1P7ovDrNb/tthxKoAOxNyMuNxDel4Nx1xPiS/9ccuJFd56vSmnAchgIQ== deployment-server
      - ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDCC8+e/lV++xqets2fX5U6Uw9/FM/mdjt7dP7I0uMk6Csl4IEA4gzvZqORy59nSm3JD4gvClEK//iDKBx6Odmx/Go2XkBIvv8c69YUsQ/niT3O1VRM1A7RDYeeY5EKTYO9IJJsOwG2/CPrxj75g1gXVOuNmBrxb/wDuV6eMs5IkQ7aPj0QXoF/qfdWxFkL3cpa3tYBTyUB/eTpZR3i5fWlNFLdH9xL8lWsmILi7PRtDzaMq0KZz3o3TpAliZZjrQPnTW1aDHXKqP8+l8630/hI67G3EkniM9aoZmVcxUZSKxYZMCD9uNSKcKRKdEPC+hElTFIZl3uHbfV/BoXSBbWSw3t3NjRhvb7F5kBCTLL40hGtE7GThEIYaMrJINkRkRtA5nJlvge8Zc8gcjuY4nSVf6OSvcyo+UOPKBxMp0MSG4M8AEMTF1pVXlugCowaNB3CPN9l6+URVx9zRUy7v3DcoEh0w2yqHmQOc/mMvLDfS09mK6nRgLZSE2O6cDBEIJ0= <EMAIL>
    shell: /bin/bash

apt:
  sources:
    php:
      source: "ppa:ondrej/php"
    ffmpeg4:
      source: "ppa:savoury1/ffmpeg4"
    graphics:
      source: "ppa:savoury1/graphics"
    multimedia:
      source: "ppa:savoury1/multimedia"
    build-tools:
      source: "ppa:savoury1/build-tools"
    nodesource:
      source: |
        deb https://deb.nodesource.com/node_10.x bionic main
        deb-src https://deb.nodesource.com/node_10.x bionic main
      key: |
        -----BEGIN PGP PUBLIC KEY BLOCK-----
        Version: GnuPG v1
        Comment: GPGTools - https://gpgtools.org

        mQINBFObJLYBEADkFW8HMjsoYRJQ4nCYC/6Eh0yLWHWfCh+/9ZSIj4w/pOe2V6V+
        W6DHY3kK3a+2bxrax9EqKe7uxkSKf95gfns+I9+R+RJfRpb1qvljURr54y35IZgs
        fMG22Np+TmM2RLgdFCZa18h0+RbH9i0b+ZrB9XPZmLb/h9ou7SowGqQ3wwOtT3Vy
        qmif0A2GCcjFTqWW6TXaY8eZJ9BCEqW3k/0Cjw7K/mSy/utxYiUIvZNKgaG/P8U7
        89QyvxeRxAf93YFAVzMXhoKxu12IuH4VnSwAfb8gQyxKRyiGOUwk0YoBPpqRnMmD
        Dl7SdmY3oQHEJzBelTMjTM8AjbB9mWoPBX5G8t4u47/FZ6PgdfmRg9hsKXhkLJc7
        C1btblOHNgDx19fzASWX+xOjZiKpP6MkEEzq1bilUFul6RDtxkTWsTa5TGixgCB/
        G2fK8I9JL/yQhDc6OGY9mjPOxMb5PgUlT8ox3v8wt25erWj9z30QoEBwfSg4tzLc
        Jq6N/iepQemNfo6Is+TG+JzI6vhXjlsBm/Xmz0ZiFPPObAH/vGCY5I6886vXQ7ft
        qWHYHT8jz/R4tigMGC+tvZ/kcmYBsLCCI5uSEP6JJRQQhHrCvOX0UaytItfsQfLm
        EYRd2F72o1yGh3yvWWfDIBXRmaBuIGXGpajC0JyBGSOWb9UxMNZY/2LJEwARAQAB
        tB9Ob2RlU291cmNlIDxncGdAbm9kZXNvdXJjZS5jb20+iQI4BBMBAgAiBQJTmyS2
        AhsDBgsJCAcDAgYVCAIJCgsEFgIDAQIeAQIXgAAKCRAWVaCraFdigHTmD/9OKhUy
        jJ+h8gMRg6ri5EQxOExccSRU0i7UHktecSs0DVC4lZG9AOzBe+Q36cym5Z1di6JQ
        kHl69q3zBdV3KTW+H1pdmnZlebYGz8paG9iQ/wS9gpnSeEyx0Enyi167Bzm0O4A1
        GK0prkLnz/yROHHEfHjsTgMvFwAnf9uaxwWgE1d1RitIWgJpAnp1DZ5O0uVlsPPm
        XAhuBJ32mU8S5BezPTuJJICwBlLYECGb1Y65Cil4OALU7T7sbUqfLCuaRKxuPtcU
        VnJ6/qiyPygvKZWhV6Od0Yxlyed1kftMJyYoL8kPHfeHJ+vIyt0s7cropfiwXoka
        1iJB5nKyt/eqMnPQ9aRpqkm9ABS/r7AauMA/9RALudQRHBdWIzfIg0Mlqb52yyTI
        IgQJHNGNX1T3z1XgZhI+Vi8SLFFSh8x9FeUZC6YJu0VXXj5iz+eZmk/nYjUt4Mtc
        pVsVYIB7oIDIbImODm8ggsgrIzqxOzQVP1zsCGek5U6QFc9GYrQ+Wv3/fG8hfkDn
        xXLww0OGaEQxfodm8cLFZ5b8JaG3+Yxfe7JkNclwvRimvlAjqIiW5OK0vvfHco+Y
        gANhQrlMnTx//IdZssaxvYytSHpPZTYw+qPEjbBJOLpoLrz8ZafN1uekpAqQjffI
        AOqW9SdIzq/kSHgl0bzWbPJPw86XzzftewjKNbkCDQRTmyS2ARAAxSSdQi+WpPQZ
        fOflkx9sYJa0cWzLl2w++FQnZ1Pn5F09D/kPMNh4qOsyvXWlekaV/SseDZtVziHJ
        Km6V8TBG3flmFlC3DWQfNNFwn5+pWSB8WHG4bTA5RyYEEYfpbekMtdoWW/Ro8Kmh
        41nuxZDSuBJhDeFIp0ccnN2Lp1o6XfIeDYPegyEPSSZqrudfqLrSZhStDlJgXjea
        JjW6UP6txPtYaaila9/Hn6vF87AQ5bR2dEWB/xRJzgNwRiax7KSU0xca6xAuf+TD
        xCjZ5pp2JwdCjquXLTmUnbIZ9LGV54UZ/MeiG8yVu6pxbiGnXo4Ekbk6xgi1ewLi
        vGmz4QRfVklV0dba3Zj0fRozfZ22qUHxCfDM7ad0eBXMFmHiN8hg3IUHTO+UdlX/
        aH3gADFAvSVDv0v8t6dGc6XE9Dr7mGEFnQMHO4zhM1HaS2Nh0TiL2tFLttLbfG5o
        QlxCfXX9/nasj3K9qnlEg9G3+4T7lpdPmZRRe1O8cHCI5imVg6cLIiBLPO16e0fK
        yHIgYswLdrJFfaHNYM/SWJxHpX795zn+iCwyvZSlLfH9mlegOeVmj9cyhN/VOmS3
        QRhlYXoA2z7WZTNoC6iAIlyIpMTcZr+ntaGVtFOLS6fwdBqDXjmSQu66mDKwU5Ek
        fNlbyrpzZMyFCDWEYo4AIR/18aGZBYUAEQEAAYkCHwQYAQIACQUCU5sktgIbDAAK
        CRAWVaCraFdigIPQEACcYh8rR19wMZZ/hgYv5so6Y1HcJNARuzmffQKozS/rxqec
        0xM3wceL1AIMuGhlXFeGd0wRv/RVzeZjnTGwhN1DnCDy1I66hUTgehONsfVanuP1
        PZKoL38EAxsMzdYgkYH6T9a4wJH/IPt+uuFTFFy3o8TKMvKaJk98+Jsp2X/QuNxh
        qpcIGaVbtQ1bn7m+k5Qe/fz+bFuUeXPivafLLlGc6KbdgMvSW9EVMO7yBy/2JE15
        ZJgl7lXKLQ31VQPAHT3an5IV2C/ie12eEqZWlnCiHV/wT+zhOkSpWdrheWfBT+ac
        hR4jDH80AS3F8jo3byQATJb3RoCYUCVc3u1ouhNZa5yLgYZ/iZkpk5gKjxHPudFb
        DdWjbGflN9k17VCf4Z9yAb9QMqHzHwIGXrb7ryFcuROMCLLVUp07PrTrRxnO9A/4
        xxECi0l/BzNxeU1gK88hEaNjIfviPR/h6Gq6KOcNKZ8rVFdwFpjbvwHMQBWhrqfu
        G3KaePvbnObKHXpfIKoAM7X2qfO+IFnLGTPyhFTcrl6vZBTMZTfZiC1XDQLuGUnd
        sckuXINIU3DFWzZGr0QrqkuE/jyr7FXeUJj9B7cLo+s/TXo+RaVfi3kOc9BoxIvy
        /qiNGs/TKy2/Ujqp/affmIMoMXSozKmga81JSwkADO1JMgUy6dApXz9kP4EE3g==
        =CLGF
        -----END PGP PUBLIC KEY BLOCK-----

package_udpate: true
packages:
  - apache2
  - software-properties-common
  - php7.4
  - php7.4-common
  - libapache2-mod-php7.4
  - php7.4-mysql
  - php7.4-imagick
  - php7.4-cli
  - php7.4-curl
  - php7.4-zip
  - php7.4-gd
  - php7.4-xml
  - php7.4-mbstring
  - php7.4-fpm
  - php7.4-xmlrpc
  - php7.4-imap
  - php7.4-bcmath
  - php7.4-opcache
  - php7.4-soap
  - php7.4-intl
  - poppler-utils
  - imagemagick
  - composer
  - git
  - awscli
  - make
  - g++
  - nodejs

runcmd:
  - aws s3 cp s3://thankview-secrets/common/thankview_deploy /root/thankview_deploy
  - chmod 0600 /root/thankview_deploy
  - mkdir /home/<USER>/root/thankview_deploy /home/<USER>/
  - chown www-data.www-data -R /home/<USER>
  - export GIT_SSH_COMMAND='ssh -i /root/thankview_deploy -o StrictHostKeyChecking=no'
  - <NAME_EMAIL>:evertrue/ThankView-App.git /var/www/thank-views
  - aws s3 cp s3://thankview-secrets/prod/prod.env /var/www/thank-views/.env
  - cp /root/htaccess /var/www/thank-views/public/.htaccess
  - chown -R www-data.www-data /var/www
  - gpasswd -a ubuntu www-data
  - cd /var/www/thank-views
  - sudo -H -u www-data composer update --no-dev
  - sudo -H -u www-data npm install
  - sudo -H -u www-data npx gulp templateCache
  - sudo -H -u www-data npx gulp --production
  - sudo -H -u www-data npx gulp purgecss
  - chmod g+sw /var/www/thank-views && chmod g+rw -R /var/www/thank-views && find /var/www/thank-views -type d -exec chmod g+s {} \;
  - a2enmod http2 ssl headers rewrite proxy_fcgi setenvif expires
  - a2enconf php7.4-fpm
  - a2dismod php7.4
  - a2dismod mpm_prefork
  - a2enmod mpm_event
  - cp /root/fpm_php.ini /etc/php/7.4/fpm/php.ini
  - cp /root/fpm_www.conf /etc/php/7.4/fpm/pool.d/www.conf
  - systemctl restart apache2

write_files:
  - encoding: b64
    content: ${vhost_file_encoded}
    owner: root:root
    path: /etc/apache2/sites-enabled/001-thankview.conf
    permissions: '0644'

  - encoding: b64
    content: ${htaccess_encoded}
    owner: root:root
    path: /root/htaccess
    permissions: '0644'

  - encoding: b64
    content: ${fpm_php_ini_encoded}
    owner: root:root
    path: /root/fpm_php.ini
    permissions: '0644'

  - encoding: b64
    content: ${fpm_www_conf_encoded}
    owner: root:root
    path: /root/fpm_www.conf
    permissions: '0644'