[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
#command=php /var/www/thank-views/artisan queue:work --tries=1 --timeout=1800 --daemon --max-jobs=2 -v
command=php /var/www/thank-views/artisan queue:work --queue=thankview-small-campaigns-mail-queue,thankview-mail-queue --tries=1 --timeout=1800 --daemon --max-jobs=2 -v
autostart=false
autorestart=true
user=ubuntu
numprocs=10
redirect_stderr=true
stdout_logfile=/var/www/thank-views/storage/logs/worker-%(process_num)02d.log
stdout_logfile_maxbytes=500MB
