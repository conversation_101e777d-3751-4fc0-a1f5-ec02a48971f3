<Files *.js.br>
    AddType "text/javascript" .br
    AddEncoding br .br
</Files>

<Files *.css.br>
    AddType "text/css" .br
    AddEncoding br .br
</Files>

<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews
    </IfModule>
    
    RewriteEngine On
    
    # Redirect Trailing Slashes. Remember to redirect to https
    RewriteRule ^(.*)/$ https://%{HTTP_HOST}/$1 [L,R=302]
    
    # Handle Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]

    # Serve pre-compressed brotil files
    RewriteCond %{HTTP:Accept-Encoding} br
    RewriteCond %{REQUEST_FILENAME}.br -f
    RewriteRule ^(.*)$ $1.br [L]
</IfModule>

<IfModule mod_headers.c>
    Header always set X-Frame-Options SAMEORIGIN
</IfModule>

<FilesMatch "^\.">
    Order allow,deny
    Deny from all
    Satisfy All
</FilesMatch>

ExpiresActive On
ExpiresByType application/javascript *********
ExpiresByType text/css *********
