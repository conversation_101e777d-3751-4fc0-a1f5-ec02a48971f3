# locals {
#   functions = {
#     elb-hostname-as-target = {
#       description = "Sets the resolved IP address of an FQDN as the target in a target group"
#       runtime     = "python3.7"
#       source_path = "./lambda-functions/elb-hostname-as-target/"
#       timeout     = 45
#       environment_variables = {
#         TARGET_FQDN            = "thankviews-ca-instance-1.clds0xxsxgil.ca-central-1.rds.amazonaws.com"
#         ELB_TG_ARN             = module.nlb["ca-rds"]
#         DNS_SERVER             = "*******"
#         S3_BUCKET              = module.s3_bucket["et-ca-nlb-rds-lambda-logs"].s3_bucket_id
#         REMOVE_UNTRACKED_TG_IP = true
#       }

#     }
#   }
# }

# module "lambda_function" {
#   source = "terraform-aws-modules/lambda/aws"

#   for_each = local.functions

#   function_name          = try(each.key, "")
#   description            = try(each.value.description, "")
#   handler                = "index.lambda_handler"
#   runtime                = try(each.value.runtime, "")
#   source_path            = try(each.value.source_path, "")
#   vpc_subnet_ids         = data.aws_subnets.private.ids
#   vpc_security_group_ids = try(each.value.vpc_security_group_ids, [])
#   attach_network_policy  = true
#   environment_variables  = try(each.value.environment_variables, {})
#   timeout                = try(each.value.timeout, 10)
# }
