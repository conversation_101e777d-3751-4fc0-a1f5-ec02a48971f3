locals {
  buckets = {
    et-ca-nlb-rds-lambda-logs = {
      versioning = {
        enabled = true
      }
    }
  }
}

module "s3_bucket" {
  source = "terraform-aws-modules/s3-bucket/aws"

  for_each = local.buckets

  # S3 Bucket Ownership Controls
  # https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_ownership_controls
  control_object_ownership = true
  object_ownership         = "BucketOwnerPreferred"

  bucket     = try(each.key, "")
  versioning = try(each.value.versioning, {})
}
