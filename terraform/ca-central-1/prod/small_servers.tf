resource "aws_instance" "prod-ca-sftp-host" {
  ami                    = data.aws_ami.ubuntu_20.id
  instance_type          = "t3a.large"
  iam_instance_profile   = "thankview-ssm-ec2-instance-role"
  key_name               = "thankview-prod-ec2"
  subnet_id              = tolist(data.aws_subnets.selected.ids)[0]
  vpc_security_group_ids = [aws_security_group.thankview-ssh-access.id, aws_security_group.sftp-group.id]

  root_block_device {
    encrypted   = true
    volume_type = "gp3"
    throughput  = 125
    volume_size = 192
    tags = {
      Name = "prod-ca-sftp-host"
    }
  }

  user_data_replace_on_change = false

  tags = {
    Name     = "prod-ca-sftp-host"
    VantaProd = "true"
  }
}

resource "aws_eip" "prod_ca_sftp_host_eip" {
  vpc = true

  tags = {
    Name = "prod_ca_sftp_host_eip"
  }
}

resource "aws_eip_association" "eip_assoc_prod_ca_sftp_host" {
  instance_id   = aws_instance.prod-ca-sftp-host.id
  allocation_id = aws_eip.prod_ca_sftp_host_eip.id
}
#------------------------------------------------

resource "aws_instance" "prod-ca-api-host" {

  ami                    = data.aws_ami.ubuntu_20.id
  instance_type          = "t3a.large"
  iam_instance_profile   = "thankview-ssm-ec2-instance-role"
  key_name               = "thankview-prod-ec2"
  subnet_id              = tolist(data.aws_subnets.selected.ids)[0]
  vpc_security_group_ids = [aws_security_group.thankview-ssh-access.id, aws_security_group.web-access-sg.id]


  root_block_device {
    encrypted   = true
    volume_type = "gp3"
    throughput  = 125
    volume_size = 32
    tags = {
      Name = "prod-ca-api-host"
    }
  }

    user_data_replace_on_change = false

  tags = {
    Name     = "prod-ca-api-host"
    VantaProd = "true"
  }
}

resource "aws_eip" "prod_ca_api_host_eip" {
  vpc = true

  tags = {
    Name = "prod_ca_api_host_eip"
  }
}

resource "aws_eip_association" "eip_assoc_prod_ca_api_host" {
  instance_id   = aws_instance.prod-ca-api-host.id
  allocation_id = aws_eip.prod_ca_api_host_eip.id
}