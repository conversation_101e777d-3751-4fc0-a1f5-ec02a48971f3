resource "aws_security_group" "thankview-ssh-access" {
  name        = "thankview-ssh-access"
  description = "Allows SSH access to developers"
  vpc_id      = data.aws_vpc.selected.id

  ingress {
    description = "Chris IP address (NJ)"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }
  ingress {
    description = "Roderick IP address"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }
  ingress {
    description = "Virginia IP address"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["**************/32"]
  }
  ingress {
    description = "Jake IP address"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["**************/32"]
  }
  ingress {
    description = "Chris IP address"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["**********/32"]
  }
  ingress {
    description = "Jerry IP address"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

}

resource "aws_security_group" "sftp-group" {
  name   = "sftp-group"
  vpc_id = data.aws_vpc.selected.id

  ingress {
    description = "ssh"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    description             = "allows RDS traffic"
    from_port               = 0
    to_port                 = 0
    protocol                = "-1" // -1 means all protocols
    security_groups         = ["sg-099e3aae2983216b7"]
  }

    egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}




resource "aws_security_group" "web-access-sg" {
  name        = "web-access-sg"
  description = "web-access-sg"
  vpc_id      = data.aws_vpc.selected.id

  ingress {
    description = "HTTPS"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  ingress {
    description = "HTTP"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

    egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

}