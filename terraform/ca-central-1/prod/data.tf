data "aws_vpc" "selected" {
  filter {
    name   = "tag:Name"
    values = ["tv-canada-vpc"]
  }
}

data "aws_subnets" "selected" {
  filter {
    name   = "tag:Name"
    values = ["tv-canada-subnet-public1-ca-central-1a"]
  }
}


data "aws_ami" "ubuntu_20" {
  most_recent = true
  owners      = ["099720109477"] # Canonical

  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd/ubuntu-focal-20.04-amd64-server-*"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
}


data "aws_route53_zone" "thankview" {
  name = "thankview.com."
}
