resource "aws_route53_record" "api_ca_record" {
  zone_id = data.aws_route53_zone.thankview.zone_id
  name    = "api-ca.thankview.com"
  type    = "A"
  ttl     = "300"
  records = [aws_eip.prod_ca_api_host_eip.public_ip]
}

resource "aws_route53_record" "sftp_ca_record" {
  zone_id = data.aws_route53_zone.thankview.zone_id
  name    = "sftp-ca.thankview.com"
  type    = "A"
  ttl     = "300"
  records = [aws_eip.prod_ca_sftp_host_eip.public_ip]
}
