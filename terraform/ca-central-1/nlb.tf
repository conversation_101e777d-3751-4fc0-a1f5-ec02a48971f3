locals {
  loadbalancers = {
    ca-rds = {
      name                  = "ca-rds"
      load_balancer_type    = "network"
      vpc_id                = data.aws_vpc.canada.id
      subnets               = data.aws_subnets.public.ids
      create_security_group = false
      access_logs           = {}
      target_groups = [
        {
          name             = "ca-rds"
          backend_protocol = "TCP"
          backend_port     = 3306
          target_type      = "ip"
        },
      ]
      http_tcp_listeners = [
        {
          port               = 3306
          protocol           = "TCP"
          target_group_index = 0
        }
      ]
      tags = "${merge(var.commonTagsGlobal, { Creator = "<EMAIL>", Component = "CA RDS NLB", CreateDate = "20230418" })}"
    }
  }
}


module "nlb" {
  source  = "terraform-aws-modules/alb/aws"
  version = "~> 8.0"

  for_each = local.loadbalancers

  name = try(each.value.name, "")

  load_balancer_type = try(each.value.load_balancer_type, "")

  vpc_id                = try(each.value.vpc_id, "")
  subnets               = try(each.value.subnets, [])
  create_security_group = try(each.value.create_security_group, false)
  security_groups       = try(each.value.security_groups, [])

  access_logs = try(each.value.access_logs, {})

  target_groups      = try(each.value.target_groups, [])
  https_listeners    = try(each.value.https_listeners, [])
  http_tcp_listeners = try(each.value.http_tcp_listeners, [])
  tags               = try(each.value.tags, {})
}
