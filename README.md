Packer is used to update AMI for sends and video servers

you will need packer installed on your local system

pull down this repo and go to packer directory

updates are usually added to the installs.sh file or main.prk.hcl file 

once the update has been added run 

packer validate -var 'version=x.x.xx' main.pkr.hcl            --------add a version number the versions are taken from the current AMIs in use 

then run 

packer build -var 'version=x.x.xx' main.pkr.hcl               --------this will build the new image (it will take alot of time)

Create new launch template using previous template as guide but using new AMI built by packer

